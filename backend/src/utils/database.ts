import { Pool, PoolClient } from "pg";
import { logger } from "./logger";

class Database {
  private pool: Pool;

  constructor() {
    // Use individual connection parameters to avoid connection string parsing issues
    const dbConfig = {
      host: process.env["DB_HOST"] || "localhost",
      port: parseInt(process.env["DB_PORT"] || "5432"),
      database: process.env["DB_NAME"] || "moflash_dev",
      user: process.env["DB_USER"] || "moflash_user",
      password: String(process.env["DB_PASSWORD"] || "moflash_password"), // Ensure password is a string
      ssl:
        process.env["NODE_ENV"] === "production"
          ? { rejectUnauthorized: false }
          : false,
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    };

    logger.debug("Database configuration", {
      host: dbConfig.host,
      port: dbConfig.port,
      database: dbConfig.database,
      user: dbConfig.user,
      passwordLength: dbConfig.password.length,
    });

    this.pool = new Pool(dbConfig);

    this.pool.on("error", (err) => {
      logger.error("Unexpected error on idle client", err);
      process.exit(-1);
    });
  }

  async query(text: string, params?: any[]): Promise<any> {
    const start = Date.now();
    try {
      const res = await this.pool.query(text, params);
      const duration = Date.now() - start;
      logger.debug("Executed query", { text, duration, rows: res.rowCount });
      return res;
    } catch (error) {
      logger.error("Database query error", { text, params, error });
      throw error;
    }
  }

  async getClient(): Promise<PoolClient> {
    return this.pool.connect();
  }

  async transaction<T>(
    callback: (client: PoolClient) => Promise<T>
  ): Promise<T> {
    const client = await this.getClient();
    try {
      await client.query("BEGIN");
      const result = await callback(client);
      await client.query("COMMIT");
      return result;
    } catch (error) {
      await client.query("ROLLBACK");
      throw error;
    } finally {
      client.release();
    }
  }

  async close(): Promise<void> {
    await this.pool.end();
  }

  // Health check
  async isHealthy(): Promise<boolean> {
    try {
      const result = await this.query("SELECT 1");
      return result.rows.length > 0;
    } catch (error) {
      logger.error("Database health check failed", error);
      return false;
    }
  }
}

export const db = new Database();

// Helper functions for common database operations
export const dbHelpers = {
  // Convert camelCase to snake_case for database columns
  toSnakeCase(obj: Record<string, any>): Record<string, any> {
    const result: Record<string, any> = {};
    for (const [key, value] of Object.entries(obj)) {
      const snakeKey = key.replace(
        /[A-Z]/g,
        (letter) => `_${letter.toLowerCase()}`
      );
      result[snakeKey] = value;
    }
    return result;
  },

  // Convert snake_case to camelCase for API responses
  toCamelCase(obj: Record<string, any>): Record<string, any> {
    const result: Record<string, any> = {};
    for (const [key, value] of Object.entries(obj)) {
      const camelKey = key.replace(/_([a-z])/g, (_, letter) =>
        letter.toUpperCase()
      );
      result[camelKey] = value;
    }
    return result;
  },

  // Build WHERE clause from filters
  buildWhereClause(
    filters: Record<string, any>,
    startIndex = 1
  ): { clause: string; values: any[] } {
    const conditions: string[] = [];
    const values: any[] = [];
    let paramIndex = startIndex;

    for (const [key, value] of Object.entries(filters)) {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          // Handle array values (IN clause)
          const placeholders = value.map(() => `$${paramIndex++}`).join(", ");
          conditions.push(`${key} IN (${placeholders})`);
          values.push(...value);
        } else if (typeof value === "string" && value.includes("%")) {
          // Handle LIKE queries
          conditions.push(`${key} ILIKE $${paramIndex++}`);
          values.push(value);
        } else {
          // Handle exact matches
          conditions.push(`${key} = $${paramIndex++}`);
          values.push(value);
        }
      }
    }

    return {
      clause: conditions.length > 0 ? `WHERE ${conditions.join(" AND ")}` : "",
      values,
    };
  },

  // Build ORDER BY clause
  buildOrderClause(
    sortBy?: string,
    sortOrder: "asc" | "desc" = "desc"
  ): string {
    if (!sortBy) return "";
    const order = sortOrder.toUpperCase();
    return `ORDER BY ${sortBy} ${order}`;
  },

  // Build LIMIT and OFFSET clause
  buildPaginationClause(
    page = 1,
    limit = 20
  ): { clause: string; offset: number } {
    const offset = (page - 1) * limit;
    return {
      clause: `LIMIT ${limit} OFFSET ${offset}`,
      offset,
    };
  },

  // Calculate total pages
  calculateTotalPages(total: number, limit: number): number {
    return Math.ceil(total / limit);
  },
};

// Database connection test
export async function testDatabaseConnection(): Promise<boolean> {
  try {
    await db.query("SELECT NOW()");
    logger.info("Database connection established successfully");
    return true;
  } catch (error) {
    logger.error("Failed to connect to database", error);
    return false;
  }
}
