import { db, dbHelpers } from "@/utils/database";
import {
  Deck,
  CreateDeckData,
  QueryOptions,
  AppError,
  PaginatedResponse,
} from "@/types";
import { logger } from "@/utils/logger";

export class DeckService {
  /**
   * Create a new deck
   */
  async create(userId: string, deckData: CreateDeckData): Promise<Deck> {
    try {
      const query = `
        INSERT INTO decks (user_id, title, description, is_public, category, tags, difficulty_level)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING id, user_id, title, description, is_public, category, tags,
                  difficulty_level, card_count, study_count, rating, rating_count,
                  created_at, updated_at, metadata
      `;

      const values = [
        userId,
        deckData.title,
        deckData.description || null,
        deckData.isPublic || false,
        deckData.category || null,
        deckData.tags || [],
        deckData.difficultyLevel || 1,
      ];

      const result = await db.query(query, values);
      const deck = dbHelpers.toCamelCase(result.rows[0]) as Deck;

      logger.info("Deck created", {
        deckId: deck.id,
        userId,
        title: deck.title,
      });
      return deck;
    } catch (error) {
      logger.error("Failed to create deck", { error, userId, deckData });
      throw error;
    }
  }

  /**
   * Find deck by ID
   */
  async findById(id: string, userId?: string): Promise<Deck | null> {
    try {
      let query = `
        SELECT id, user_id, title, description, is_public, category, tags,
               difficulty_level, card_count, study_count, rating, rating_count,
               created_at, updated_at, metadata
        FROM decks
        WHERE id = $1
      `;

      const values = [id];

      // If userId is provided, check ownership or public visibility
      if (userId) {
        query += ` AND (user_id = $2 OR is_public = true)`;
        values.push(userId);
      } else {
        query += ` AND is_public = true`;
      }

      const result = await db.query(query, values);

      if (result.rows.length === 0) {
        return null;
      }

      return dbHelpers.toCamelCase(result.rows[0]) as Deck;
    } catch (error) {
      logger.error("Failed to find deck by ID", { error, id, userId });
      throw error;
    }
  }

  /**
   * Find decks by user ID
   */
  async findByUserId(
    userId: string,
    options: QueryOptions = {}
  ): Promise<PaginatedResponse<Deck>> {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = "created_at",
        sortOrder = "desc",
        search,
        filters = {},
      } = options;

      let whereClause = "WHERE user_id = $1";
      const values = [userId];
      let paramIndex = 2;

      // Add search functionality
      if (search) {
        whereClause += ` AND (title ILIKE $${paramIndex} OR description ILIKE $${paramIndex})`;
        values.push(`%${search}%`);
        paramIndex++;
      }

      // Add filters
      if (filters.category) {
        whereClause += ` AND category = $${paramIndex}`;
        values.push(filters.category);
        paramIndex++;
      }

      if (filters.difficultyLevel) {
        whereClause += ` AND difficulty_level = $${paramIndex}`;
        values.push(filters.difficultyLevel);
        paramIndex++;
      }

      if (filters.tags && filters.tags.length > 0) {
        whereClause += ` AND tags && $${paramIndex}`;
        values.push(filters.tags);
        paramIndex++;
      }

      // Build order clause
      const orderClause = dbHelpers.buildOrderClause(sortBy, sortOrder);

      // Build pagination clause
      const { clause: paginationClause, offset } =
        dbHelpers.buildPaginationClause(page, limit);

      // Get total count
      const countQuery = `SELECT COUNT(*) FROM decks ${whereClause}`;
      const countResult = await db.query(countQuery, values);
      const total = parseInt(countResult.rows[0].count);

      // Get decks
      const query = `
        SELECT id, user_id, title, description, is_public, category, tags,
               difficulty_level, card_count, study_count, rating, rating_count,
               created_at, updated_at, metadata
        FROM decks
        ${whereClause}
        ${orderClause}
        ${paginationClause}
      `;

      const result = await db.query(query, values);
      const decks = result.rows.map((row: any) =>
        dbHelpers.toCamelCase(row)
      ) as Deck[];

      return {
        data: decks,
        pagination: {
          page,
          limit,
          total,
          totalPages: dbHelpers.calculateTotalPages(total, limit),
        },
      };
    } catch (error) {
      logger.error("Failed to find decks by user ID", {
        error,
        userId,
        options,
      });
      throw error;
    }
  }

  /**
   * Find public decks
   */
  async findPublic(
    options: QueryOptions = {}
  ): Promise<PaginatedResponse<Deck>> {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = "rating",
        sortOrder = "desc",
        search,
        filters = {},
      } = options;

      let whereClause = "WHERE is_public = true";
      const values: any[] = [];
      let paramIndex = 1;

      // Add search functionality
      if (search) {
        whereClause += ` AND (title ILIKE $${paramIndex} OR description ILIKE $${paramIndex})`;
        values.push(`%${search}%`);
        paramIndex++;
      }

      // Add filters
      if (filters.category) {
        whereClause += ` AND category = $${paramIndex}`;
        values.push(filters.category);
        paramIndex++;
      }

      if (filters.difficultyLevel) {
        whereClause += ` AND difficulty_level = $${paramIndex}`;
        values.push(filters.difficultyLevel);
        paramIndex++;
      }

      if (filters.tags && filters.tags.length > 0) {
        whereClause += ` AND tags && $${paramIndex}`;
        values.push(filters.tags);
        paramIndex++;
      }

      // Build order clause
      const orderClause = dbHelpers.buildOrderClause(sortBy, sortOrder);

      // Build pagination clause
      const { clause: paginationClause } = dbHelpers.buildPaginationClause(
        page,
        limit
      );

      // Get total count
      const countQuery = `SELECT COUNT(*) FROM decks ${whereClause}`;
      const countResult = await db.query(countQuery, values);
      const total = parseInt(countResult.rows[0].count);

      // Get decks with user information
      const query = `
        SELECT d.id, d.user_id, d.title, d.description, d.is_public, d.category, d.tags,
               d.difficulty_level, d.card_count, d.study_count, d.rating, d.rating_count,
               d.created_at, d.updated_at, d.metadata,
               u.username as author_username, u.avatar_url as author_avatar
        FROM decks d
        JOIN users u ON d.user_id = u.id
        ${whereClause}
        ${orderClause}
        ${paginationClause}
      `;

      const result = await db.query(query, values);
      const decks = result.rows.map((row: any) => {
        const deck = dbHelpers.toCamelCase(row) as any;
        deck.author = {
          username: deck.authorUsername,
          avatarUrl: deck.authorAvatar,
        };
        delete deck.authorUsername;
        delete deck.authorAvatar;
        return deck;
      }) as Deck[];

      return {
        data: decks,
        pagination: {
          page,
          limit,
          total,
          totalPages: dbHelpers.calculateTotalPages(total, limit),
        },
      };
    } catch (error) {
      logger.error("Failed to find public decks", { error, options });
      throw error;
    }
  }

  /**
   * Update deck
   */
  async update(
    id: string,
    userId: string,
    updates: Partial<Deck>
  ): Promise<Deck> {
    try {
      // Check ownership
      const existingDeck = await this.findById(id, userId);
      if (!existingDeck) {
        throw new AppError("Deck not found", 404);
      }

      if (existingDeck.userId !== userId) {
        throw new AppError("Not authorized to update this deck", 403);
      }

      const allowedFields = [
        "title",
        "description",
        "is_public",
        "category",
        "tags",
        "difficulty_level",
      ];
      const updateData = dbHelpers.toSnakeCase(updates);

      // Filter only allowed fields
      const filteredUpdates = Object.keys(updateData)
        .filter((key) => allowedFields.includes(key))
        .reduce((obj, key) => {
          obj[key] = updateData[key];
          return obj;
        }, {} as any);

      if (Object.keys(filteredUpdates).length === 0) {
        throw new AppError("No valid fields to update", 400);
      }

      const setClause = Object.keys(filteredUpdates)
        .map((key, index) => `${key} = $${index + 2}`)
        .join(", ");

      const query = `
        UPDATE decks
        SET ${setClause}, updated_at = NOW()
        WHERE id = $1
        RETURNING id, user_id, title, description, is_public, category, tags,
                  difficulty_level, card_count, study_count, rating, rating_count,
                  created_at, updated_at, metadata
      `;

      const values = [id, ...Object.values(filteredUpdates)];
      const result = await db.query(query, values);

      const deck = dbHelpers.toCamelCase(result.rows[0]) as Deck;
      logger.info("Deck updated", { deckId: id, userId });
      return deck;
    } catch (error) {
      logger.error("Failed to update deck", { error, id, userId, updates });
      throw error;
    }
  }

  /**
   * Delete deck
   */
  async delete(id: string, userId: string): Promise<void> {
    try {
      // Check ownership
      const existingDeck = await this.findById(id, userId);
      if (!existingDeck) {
        throw new AppError("Deck not found", 404);
      }

      if (existingDeck.userId !== userId) {
        throw new AppError("Not authorized to delete this deck", 403);
      }

      const query = "DELETE FROM decks WHERE id = $1";
      const result = await db.query(query, [id]);

      if (result.rowCount === 0) {
        throw new AppError("Deck not found", 404);
      }

      logger.info("Deck deleted", { deckId: id, userId });
    } catch (error) {
      logger.error("Failed to delete deck", { error, id, userId });
      throw error;
    }
  }

  /**
   * Get deck statistics
   */
  async getStats(id: string, userId?: string): Promise<any> {
    try {
      const deck = await this.findById(id, userId);
      if (!deck) {
        throw new AppError("Deck not found", 404);
      }

      const query = `
        SELECT 
          COUNT(*) as total_cards,
          COUNT(CASE WHEN difficulty = 1 THEN 1 END) as easy_cards,
          COUNT(CASE WHEN difficulty = 2 THEN 1 END) as medium_cards,
          COUNT(CASE WHEN difficulty = 3 THEN 1 END) as hard_cards,
          COUNT(CASE WHEN difficulty = 4 THEN 1 END) as very_hard_cards,
          COUNT(CASE WHEN difficulty = 5 THEN 1 END) as expert_cards
        FROM cards
        WHERE deck_id = $1
      `;

      const result = await db.query(query, [id]);
      return dbHelpers.toCamelCase(result.rows[0]);
    } catch (error) {
      logger.error("Failed to get deck stats", { error, id, userId });
      throw error;
    }
  }
}

export const deckService = new DeckService();
