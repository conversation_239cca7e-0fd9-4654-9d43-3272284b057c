import { Request, Response, NextFunction } from "express";
import { authUtils } from "@/utils/auth";
import { userService } from "@/services/userService";
import { logger } from "@/utils/logger";

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: any;
    }
  }
}

/**
 * Middleware to authenticate JWT tokens
 */
export const authenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authUtils.extractTokenFromHeader(authHeader);

    if (!token) {
      res.status(401).json({
        success: false,
        error: "Access token required",
      });
      return;
    }

    // Verify token
    const decoded = authUtils.verifyToken(token);

    // Get user from database
    const user = await userService.findById(decoded.sub);
    if (!user) {
      res.status(401).json({
        success: false,
        error: "User not found",
      });
      return;
    }

    // Check if subscription is active
    if (!authUtils.isSubscriptionActive(user)) {
      res.status(403).json({
        success: false,
        error: "Subscription expired",
      });
      return;
    }

    // Attach user to request
    req.user = user;
    next();
  } catch (error) {
    logger.warn("Authentication failed", { error: (error as Error).message });
    res.status(401).json({
      success: false,
      error: "Invalid or expired token",
    });
    return;
  }
};

/**
 * Middleware to check if user has required subscription tier
 */
export const requireTier = (requiredTier: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: "Authentication required",
      });
      return;
    }

    if (!authUtils.hasRequiredTier(req.user.subscriptionTier, requiredTier)) {
      res.status(403).json({
        success: false,
        error: `${requiredTier} subscription required`,
      });
      return;
    }

    next();
  };
};

/**
 * Optional authentication middleware - doesn't fail if no token provided
 */
export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authUtils.extractTokenFromHeader(authHeader);

    if (token) {
      const decoded = authUtils.verifyToken(token);
      const user = await userService.findById(decoded.sub);
      if (user && authUtils.isSubscriptionActive(user)) {
        req.user = user;
      }
    }
  } catch (error) {
    // Silently fail for optional auth
    logger.debug("Optional auth failed", { error: (error as Error).message });
  }

  next();
};

/**
 * Middleware to check if user owns the resource
 */
export const requireOwnership = (
  resourceIdParam: string = "id",
  userIdField: string = "userId"
) => {
  return async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: "Authentication required",
        });
        return;
      }

      const resourceId = req.params[resourceIdParam];
      if (!resourceId) {
        res.status(400).json({
          success: false,
          error: "Resource ID required",
        });
        return;
      }

      // This would need to be implemented per resource type
      // For now, we'll assume the resource check is done in the controller
      next();
    } catch (error) {
      logger.error("Ownership check failed", error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
      });
      return;
    }
  };
};

/**
 * Middleware to validate API key for external integrations
 */
export const validateApiKey = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const apiKey = req.headers["x-api-key"] as string;

    if (!apiKey) {
      res.status(401).json({
        success: false,
        error: "API key required",
      });
      return;
    }

    // TODO: Implement API key validation logic
    // For now, just check if it's a valid format
    if (apiKey.length < 32) {
      res.status(401).json({
        success: false,
        error: "Invalid API key",
      });
      return;
    }

    next();
  } catch (error) {
    logger.error("API key validation failed", error);
    res.status(500).json({
      success: false,
      error: "Internal server error",
    });
    return;
  }
};

/**
 * Middleware to refresh token if it's about to expire
 */
export const refreshTokenIfNeeded = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user) {
      return next();
    }

    const authHeader = req.headers.authorization;
    const token = authUtils.extractTokenFromHeader(authHeader);

    if (token) {
      const decoded = authUtils.verifyToken(token);
      const timeUntilExpiry = decoded.exp - Math.floor(Date.now() / 1000);

      // If token expires in less than 5 minutes, include new token in response
      if (timeUntilExpiry < 300) {
        const newTokens = authUtils.generateTokens(req.user);
        res.setHeader("X-New-Access-Token", newTokens.accessToken);
        res.setHeader("X-Token-Expires-In", newTokens.expiresIn.toString());
      }
    }

    next();
  } catch (error) {
    // Don't fail the request if token refresh fails
    logger.debug("Token refresh check failed", {
      error: (error as Error).message,
    });
    next();
  }
};
