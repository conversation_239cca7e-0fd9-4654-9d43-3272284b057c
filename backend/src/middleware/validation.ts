import { Request, Response, NextFunction } from "express";
import { z, ZodSchema } from "zod";
import { logger } from "@/utils/logger";

/**
 * Generic validation middleware factory
 */
export const validate = (schema: ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      schema.parse({
        body: req.body,
        query: req.query,
        params: req.params,
      });
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        }));

        res.status(400).json({
          success: false,
          error: "Validation failed",
          details: errors,
        });
        return;
      }

      logger.error("Validation middleware error", error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
      });
      return;
    }
  };
};

// Common validation schemas
export const schemas: Record<string, ZodSchema> = {
  // User schemas
  register: z.object({
    body: z.object({
      email: z.string().email("Invalid email format"),
      username: z
        .string()
        .min(3, "Username must be at least 3 characters")
        .max(50, "Username must be less than 50 characters")
        .regex(
          /^[a-zA-Z0-9_]+$/,
          "Username can only contain letters, numbers, and underscores"
        ),
      password: z
        .string()
        .min(8, "Password must be at least 8 characters")
        .max(128, "Password must be less than 128 characters"),
      firstName: z.string().max(100).optional(),
      lastName: z.string().max(100).optional(),
    }),
  }),

  login: z.object({
    body: z.object({
      email: z.string().email("Invalid email format"),
      password: z.string().min(1, "Password is required"),
    }),
  }),

  updateProfile: z.object({
    body: z.object({
      firstName: z.string().max(100).optional(),
      lastName: z.string().max(100).optional(),
      preferences: z.record(z.any()).optional(),
    }),
  }),

  // Deck schemas
  createDeck: z.object({
    body: z.object({
      title: z
        .string()
        .min(1, "Title is required")
        .max(255, "Title must be less than 255 characters"),
      description: z.string().max(1000).optional(),
      isPublic: z.boolean().default(false),
      category: z.string().max(100).optional(),
      tags: z
        .array(z.string().max(50))
        .max(10, "Maximum 10 tags allowed")
        .default([]),
      difficultyLevel: z.number().int().min(1).max(5).default(1),
    }),
  }),

  updateDeck: z.object({
    body: z.object({
      title: z
        .string()
        .min(1, "Title is required")
        .max(255, "Title must be less than 255 characters")
        .optional(),
      description: z.string().max(1000).optional(),
      isPublic: z.boolean().optional(),
      category: z.string().max(100).optional(),
      tags: z
        .array(z.string().max(50))
        .max(10, "Maximum 10 tags allowed")
        .optional(),
      difficultyLevel: z.number().int().min(1).max(5).optional(),
    }),
  }),

  // Card schemas
  cardContent: z.object({
    type: z.enum(["text", "multimedia"]),
    content: z.string().max(10000).optional(),
    elements: z
      .array(
        z.object({
          type: z.enum(["text", "image", "audio", "video", "latex", "drawing"]),
          content: z.string().max(10000),
          style: z.record(z.string()).optional(),
          metadata: z.record(z.any()).optional(),
        })
      )
      .optional(),
  }),

  createCard: z.object({
    body: z.object({
      frontContent: z.lazy(() => schemas.cardContent),
      backContent: z.lazy(() => schemas.cardContent),
      cardType: z
        .enum([
          "basic",
          "cloze",
          "multiple_choice",
          "true_false",
          "image_occlusion",
        ])
        .default("basic"),
      difficulty: z.number().int().min(1).max(5).default(1),
      tags: z.array(z.string().max(50)).max(10).default([]),
      position: z.number().int().min(0).optional(),
    }),
  }),

  updateCard: z.object({
    body: z.object({
      frontContent: z.lazy(() => schemas.cardContent).optional(),
      backContent: z.lazy(() => schemas.cardContent).optional(),
      cardType: z
        .enum([
          "basic",
          "cloze",
          "multiple_choice",
          "true_false",
          "image_occlusion",
        ])
        .optional(),
      difficulty: z.number().int().min(1).max(5).optional(),
      tags: z.array(z.string().max(50)).max(10).optional(),
      position: z.number().int().min(0).optional(),
    }),
  }),

  // Study session schemas
  createSession: z.object({
    body: z.object({
      deckId: z.string().uuid("Invalid deck ID"),
      sessionType: z
        .enum(["review", "learn", "practice", "cram"])
        .default("review"),
      maxCards: z.number().int().min(1).max(100).optional(),
      maxDuration: z.number().int().min(60).max(7200).optional(), // 1 minute to 2 hours
    }),
  }),

  // Card review schemas
  createReview: z.object({
    body: z.object({
      cardId: z.string().uuid("Invalid card ID"),
      sessionId: z.string().uuid("Invalid session ID").optional(),
      quality: z
        .number()
        .int()
        .min(0)
        .max(5, "Quality must be between 0 and 5"),
      responseTimeMs: z.number().int().min(0).optional(),
    }),
  }),

  // Query parameter schemas
  pagination: z.object({
    query: z.object({
      page: z.string().regex(/^\d+$/).transform(Number).default("1"),
      limit: z.string().regex(/^\d+$/).transform(Number).default("20"),
      sortBy: z.string().optional(),
      sortOrder: z.enum(["asc", "desc"]).default("desc"),
    }),
  }),

  deckFilters: z.object({
    query: z.object({
      category: z.string().optional(),
      tags: z.string().optional(), // Comma-separated tags
      isPublic: z
        .string()
        .transform((val) => val === "true")
        .optional(),
      search: z.string().optional(),
      difficultyLevel: z.string().regex(/^\d+$/).transform(Number).optional(),
    }),
  }),

  // UUID parameter validation
  uuidParam: z.object({
    params: z.object({
      id: z.string().uuid("Invalid ID format"),
    }),
  }),

  deckIdParam: z.object({
    params: z.object({
      deckId: z.string().uuid("Invalid deck ID format"),
    }),
  }),

  cardIdParam: z.object({
    params: z.object({
      cardId: z.string().uuid("Invalid card ID format"),
    }),
  }),
};

// Validation middleware for common patterns
export const validateUUID = validate(schemas.uuidParam);
export const validateDeckId = validate(schemas.deckIdParam);
export const validateCardId = validate(schemas.cardIdParam);
export const validatePagination = validate(schemas.pagination);

// Custom validation functions
export const validateFileUpload = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const file = req.file;

  if (!file) {
    res.status(400).json({
      success: false,
      error: "File is required",
    });
    return;
  }

  // Check file size (10MB limit)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    res.status(400).json({
      success: false,
      error: "File size exceeds 10MB limit",
    });
    return;
  }

  // Check file type
  const allowedTypes = [
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/webp",
    "audio/mpeg",
    "audio/wav",
    "video/mp4",
  ];
  if (!allowedTypes.includes(file.mimetype)) {
    res.status(400).json({
      success: false,
      error: "Invalid file type",
    });
    return;
  }

  next();
};

export const validateOptionalFileUpload = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  if (!req.file) {
    return next();
  }

  validateFileUpload(req, res, next);
};
