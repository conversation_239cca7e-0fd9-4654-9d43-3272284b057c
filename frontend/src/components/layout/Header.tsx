import React from "react";
import { <PERSON>u, <PERSON>, Bell, LogOut } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useTheme } from "@/contexts/ThemeContext";
import { Button } from "@/components/ui/Button";
import { getInitials } from "@/lib/utils";

interface HeaderProps {
  readonly onMenuClick: () => void;
}

export function Header({ onMenuClick }: HeaderProps) {
  const { user, logout } = useAuth();
  const { setTheme, isDark } = useTheme();

  return (
    <header className="flex h-16 items-center justify-between border-b border-gray-200 bg-white px-6 dark:border-gray-700 dark:bg-gray-800">
      {/* Left side */}
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={onMenuClick}
          className="lg:hidden"
        >
          <Menu className="h-5 w-5" />
        </Button>

        {/* Search */}
        <div className="relative hidden md:block">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search decks, cards..."
            aria-label="Search decks and cards"
            className="w-64 rounded-lg border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-200 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:ring-offset-gray-800"
          />
        </div>
      </div>

      {/* Right side */}
      <div className="flex items-center space-x-4">
        {/* Theme toggle */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setTheme(isDark ? "light" : "dark")}
        >
          {isDark ? "🌞" : "🌙"}
        </Button>

        {/* Notifications */}
        <Button variant="ghost" size="sm" className="relative">
          <Bell className="h-5 w-5" />
          <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-red-500"></span>
        </Button>

        {/* User menu */}
        <div className="relative">
          <div className="flex items-center space-x-3">
            <div className="hidden text-right md:block">
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {user?.firstName} {user?.lastName}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {user?.subscriptionTier}
              </p>
            </div>

            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary-600 text-sm font-medium text-white">
              {user?.avatarUrl ? (
                <img
                  src={user.avatarUrl}
                  alt={user.username}
                  className="h-8 w-8 rounded-full"
                />
              ) : (
                getInitials(
                  ((user?.firstName ?? "") + " " + (user?.lastName ?? "") ||
                    user?.username) ??
                    "U"
                )
              )}
            </div>
          </div>

          {/* Dropdown menu would go here */}
        </div>

        {/* Quick logout */}
        <Button
          variant="ghost"
          size="sm"
          onClick={logout}
          className="text-red-600 hover:text-red-700 dark:text-red-400"
        >
          <LogOut className="h-5 w-5" />
        </Button>
      </div>
    </header>
  );
}
