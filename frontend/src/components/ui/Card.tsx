import React from 'react';
import { cn } from '@/lib/utils';

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'outlined' | 'glass';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  hover?: boolean;
}

interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {}

interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {}

interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {}

export function Card({
  className,
  variant = 'default',
  padding = 'md',
  hover = false,
  children,
  ...props
}: Readonly<CardProps>) {
  const variantClasses = {
    default: 'card',
    elevated: 'card shadow-lg',
    outlined: 'border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 rounded-xl',
    glass: 'glass-effect dark:glass-effect-dark rounded-xl border border-white/20',
  };

  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
  };

  const hoverClasses = hover ? 'hover:shadow-lg hover:-translate-y-1 transition-all duration-300' : '';

  return (
    <div
      className={cn(
        variantClasses[variant],
        paddingClasses[padding],
        hoverClasses,
        'animate-fade-in',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

export function CardHeader({
  className,
  children,
  ...props
}: Readonly<CardHeaderProps>) {
  return (
    <div
      className={cn(
        'border-b border-gray-200 dark:border-gray-700 pb-4 mb-4',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

export function CardContent({
  className,
  children,
  ...props
}: Readonly<CardContentProps>) {
  return (
    <div
      className={cn('space-y-4', className)}
      {...props}
    >
      {children}
    </div>
  );
}

export function CardFooter({
  className,
  children,
  ...props
}: Readonly<CardFooterProps>) {
  return (
    <div
      className={cn(
        'border-t border-gray-200 dark:border-gray-700 pt-4 mt-4',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}
