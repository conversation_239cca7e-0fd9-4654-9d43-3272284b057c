import React from "react";
import { cn } from "@/lib/utils";
import { LoadingSpinner } from "./LoadingSpinner";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary" | "outline" | "ghost" | "danger";
  size?: "sm" | "md" | "lg";
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

export function Button({
  children,
  className,
  variant = "primary",
  size = "md",
  isLoading = false,
  leftIcon,
  rightIcon,
  disabled,
  ...props
}: ButtonProps) {
  const baseClasses = "btn focus-ring-primary relative overflow-hidden";

  const variantClasses = {
    primary:
      "btn-primary shadow-sm hover:shadow-md transition-all duration-200",
    secondary:
      "btn-secondary shadow-sm hover:shadow-md transition-all duration-200",
    outline:
      "btn-outline shadow-sm hover:shadow-md transition-all duration-200",
    ghost: "btn-ghost transition-all duration-200",
    danger:
      "bg-red-600 text-white hover:bg-red-700 active:bg-red-800 shadow-sm hover:shadow-md transition-all duration-200",
  };

  const sizeClasses = {
    sm: "btn-sm",
    md: "btn-md",
    lg: "btn-lg",
  };

  const isDisabled = disabled || isLoading;

  return (
    <button
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        isDisabled && "opacity-50 cursor-not-allowed",
        className
      )}
      disabled={isDisabled}
      aria-disabled={isDisabled}
      {...props}
    >
      {/* Ripple effect overlay */}
      <span className="absolute inset-0 bg-white opacity-0 transition-opacity duration-200 hover:opacity-10" />

      {/* Content */}
      <span className="relative flex items-center justify-center">
        {isLoading && <LoadingSpinner size="sm" className="mr-2" />}
        {!isLoading && leftIcon && (
          <span className="mr-2 flex-shrink-0">{leftIcon}</span>
        )}

        {children}

        {rightIcon && !isLoading && (
          <span className="ml-2 flex-shrink-0">{rightIcon}</span>
        )}
      </span>
    </button>
  );
}
