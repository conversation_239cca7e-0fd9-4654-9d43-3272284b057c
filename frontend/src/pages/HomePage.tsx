import React from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Users } from "lucide-react";
import { Button } from "@/components/ui/Button";
import { useAuth } from "@/contexts/AuthContext";

export function HomePage() {
  const { isAuthenticated } = useAuth();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float dark:bg-primary-800"></div>
        <div
          className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float"
          style={{ animationDelay: "2s" }}
        ></div>
        <div
          className="absolute top-40 left-1/2 w-80 h-80 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float"
          style={{ animationDelay: "4s" }}
        ></div>
      </div>

      {/* Header */}
      <header className="relative z-10">
        <nav className="mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8">
          <div className="flex items-center space-x-2 animate-fade-in">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary-600 shadow-lg">
              <Zap className="h-5 w-5 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-900 dark:text-white">
              MoFlash
            </span>
          </div>

          <div
            className="flex items-center space-x-4 animate-fade-in"
            style={{ animationDelay: "0.2s" }}
          >
            {isAuthenticated ? (
              <Link to="/dashboard">
                <Button>Go to Dashboard</Button>
              </Link>
            ) : (
              <>
                <Link to="/login">
                  <Button variant="ghost">Sign In</Button>
                </Link>
                <Link to="/register">
                  <Button>Get Started</Button>
                </Link>
              </>
            )}
          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <section className="relative px-6 py-24 sm:py-32 lg:px-8">
        <div className="mx-auto max-w-4xl text-center">
          <h1 className="text-4xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-6xl animate-fade-in">
            Master Any Subject with{" "}
            <span className="gradient-text animate-pulse-slow">AI-Powered</span>{" "}
            Flashcards
          </h1>

          <p
            className="mt-6 text-lg leading-8 text-gray-600 dark:text-gray-300 animate-fade-in text-balance"
            style={{ animationDelay: "0.3s" }}
          >
            Transform your learning with intelligent spaced repetition,
            multimedia cards, and personalized study sessions. Join thousands of
            students and professionals who've accelerated their learning with
            MoFlash.
          </p>

          <div
            className="mt-10 flex flex-col sm:flex-row items-center justify-center gap-4 animate-fade-in"
            style={{ animationDelay: "0.6s" }}
          >
            {isAuthenticated ? (
              <Link to="/dashboard" className="w-full sm:w-auto">
                <Button
                  size="lg"
                  rightIcon={<ArrowRight className="h-5 w-5" />}
                  className="w-full"
                >
                  Continue Learning
                </Button>
              </Link>
            ) : (
              <>
                <Link to="/register" className="w-full sm:w-auto">
                  <Button
                    size="lg"
                    rightIcon={<ArrowRight className="h-5 w-5" />}
                    className="w-full"
                  >
                    Start Learning Free
                  </Button>
                </Link>
                <Link to="/login" className="w-full sm:w-auto">
                  <Button variant="outline" size="lg" className="w-full">
                    Sign In
                  </Button>
                </Link>
              </>
            )}
          </div>

          {/* Trust indicators */}
          <div
            className="mt-16 animate-fade-in"
            style={{ animationDelay: "0.9s" }}
          >
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
              Trusted by learners worldwide
            </p>
            <div className="grid grid-cols-3 gap-8 max-w-lg mx-auto">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                  50K+
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Active Users
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                  10M+
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Cards Studied
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                  94%
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Success Rate
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 sm:py-32 relative">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl animate-fade-in">
              Everything you need to learn effectively
            </h2>
            <p
              className="mt-6 text-lg leading-8 text-gray-600 dark:text-gray-300 animate-fade-in text-balance"
              style={{ animationDelay: "0.2s" }}
            >
              Powerful features designed to maximize your learning potential
            </p>
          </div>

          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
              <div
                className="flex flex-col animate-fade-in card p-6 hover:shadow-lg transition-all duration-300"
                style={{ animationDelay: "0.4s" }}
              >
                <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900 dark:text-white">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary-100 dark:bg-primary-900">
                    <Brain className="h-5 w-5 flex-none text-primary-600 dark:text-primary-400" />
                  </div>
                  Smart Spaced Repetition
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600 dark:text-gray-300">
                  <p className="flex-auto">
                    Our AI-powered algorithm adapts to your learning pace,
                    showing you cards exactly when you need to review them for
                    maximum retention.
                  </p>
                </dd>
              </div>

              <div
                className="flex flex-col animate-fade-in card p-6 hover:shadow-lg transition-all duration-300"
                style={{ animationDelay: "0.6s" }}
              >
                <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900 dark:text-white">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900">
                    <BookOpen className="h-5 w-5 flex-none text-green-600 dark:text-green-400" />
                  </div>
                  Rich Media Cards
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600 dark:text-gray-300">
                  <p className="flex-auto">
                    Create engaging flashcards with images, audio, video, and
                    LaTeX equations. Perfect for visual learners and complex
                    subjects.
                  </p>
                </dd>
              </div>

              <div
                className="flex flex-col animate-fade-in card p-6 hover:shadow-lg transition-all duration-300"
                style={{ animationDelay: "0.8s" }}
              >
                <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900 dark:text-white">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-900">
                    <Users className="h-5 w-5 flex-none text-purple-600 dark:text-purple-400" />
                  </div>
                  Collaborative Learning
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600 dark:text-gray-300">
                  <p className="flex-auto">
                    Share decks with classmates, discover community-created
                    content, and learn together with study groups and
                    leaderboards.
                  </p>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="bg-primary-600 py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:max-w-none">
            <div className="text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Trusted by learners worldwide
              </h2>
              <p className="mt-4 text-lg leading-8 text-primary-200">
                Join the community that's revolutionizing how we learn
              </p>
            </div>
            <dl className="mt-16 grid grid-cols-1 gap-0.5 overflow-hidden rounded-2xl text-center sm:grid-cols-2 lg:grid-cols-4">
              <div className="flex flex-col bg-primary-700 p-8">
                <dt className="text-sm font-semibold leading-6 text-primary-200">
                  Active Users
                </dt>
                <dd className="order-first text-3xl font-bold tracking-tight text-white">
                  50K+
                </dd>
              </div>
              <div className="flex flex-col bg-primary-700 p-8">
                <dt className="text-sm font-semibold leading-6 text-primary-200">
                  Cards Studied
                </dt>
                <dd className="order-first text-3xl font-bold tracking-tight text-white">
                  10M+
                </dd>
              </div>
              <div className="flex flex-col bg-primary-700 p-8">
                <dt className="text-sm font-semibold leading-6 text-primary-200">
                  Study Sessions
                </dt>
                <dd className="order-first text-3xl font-bold tracking-tight text-white">
                  2M+
                </dd>
              </div>
              <div className="flex flex-col bg-primary-700 p-8">
                <dt className="text-sm font-semibold leading-6 text-primary-200">
                  Success Rate
                </dt>
                <dd className="order-first text-3xl font-bold tracking-tight text-white">
                  94%
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
              Ready to supercharge your learning?
            </h2>
            <p className="mt-6 text-lg leading-8 text-gray-600 dark:text-gray-300">
              Start your free account today and experience the future of
              studying.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              {!isAuthenticated && (
                <Link to="/register">
                  <Button
                    size="lg"
                    rightIcon={<ArrowRight className="h-5 w-5" />}
                  >
                    Get Started Free
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800">
        <div className="mx-auto max-w-7xl px-6 py-12 lg:px-8">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-2">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary-600">
                <Zap className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900 dark:text-white">
                MoFlash
              </span>
            </div>
            <p className="mt-4 text-sm text-gray-600 dark:text-gray-400">
              © 2024 MoFlash. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
