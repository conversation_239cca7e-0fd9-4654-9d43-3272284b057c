import React from "react";
import { <PERSON> } from "react-router-dom";
import { Plus, <PERSON><PERSON><PERSON>, Brain, TrendingUp, Target } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/Button";
import { Card, CardHeader, CardContent } from "@/components/ui/Card";
import { Badge } from "@/components/ui/Badge";
import { Progress } from "@/components/ui/Progress";
import { getTimeOfDayGreeting } from "@/lib/utils";

export function DashboardPage() {
  const { user } = useAuth();

  // Mock data - in real app, this would come from API
  const stats = {
    totalDecks: 12,
    totalCards: 248,
    studyStreak: 7,
    cardsStudiedToday: 23,
    accuracyRate: 0.87,
    totalStudyTime: 1440, // minutes
  };

  const recentDecks = [
    {
      id: "1",
      title: "Spanish Vocabulary - Basics",
      cardCount: 45,
      lastStudied: "2 hours ago",
      progress: 0.75,
    },
    {
      id: "2",
      title: "JavaScript Fundamentals",
      cardCount: 32,
      lastStudied: "1 day ago",
      progress: 0.6,
    },
    {
      id: "3",
      title: "Biology - Cell Structure",
      cardCount: 28,
      lastStudied: "3 days ago",
      progress: 0.45,
    },
  ];

  const dueCards = [
    { id: "1", deckTitle: "Spanish Vocabulary", count: 8 },
    { id: "2", deckTitle: "JavaScript Fundamentals", count: 5 },
    { id: "3", deckTitle: "Biology - Cell Structure", count: 12 },
  ];

  return (
    <div className="space-y-8">
      {/* Welcome Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 animate-fade-in">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {getTimeOfDayGreeting()}, {user?.firstName ?? user?.username}! 👋
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Ready to continue your learning journey?
          </p>
        </div>

        <Link to="/decks/new" className="w-full sm:w-auto">
          <Button
            leftIcon={<Plus className="h-4 w-4" />}
            className="w-full sm:w-auto"
          >
            New Deck
          </Button>
        </Link>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <div
          className="card p-6 hover:shadow-lg transition-all duration-300 animate-fade-in"
          style={{ animationDelay: "0.1s" }}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900">
                <BookOpen className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Total Decks
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {stats.totalDecks}
              </p>
            </div>
          </div>
        </div>

        <div
          className="card p-6 hover:shadow-lg transition-all duration-300 animate-fade-in"
          style={{ animationDelay: "0.2s" }}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900">
                <Brain className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Cards Studied Today
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {stats.cardsStudiedToday}
              </p>
            </div>
          </div>
        </div>

        <div
          className="card p-6 hover:shadow-lg transition-all duration-300 animate-fade-in"
          style={{ animationDelay: "0.3s" }}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-900">
                <Target className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Accuracy Rate
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {Math.round(stats.accuracyRate * 100)}%
              </p>
            </div>
          </div>
        </div>

        <div
          className="card p-6 hover:shadow-lg transition-all duration-300 animate-fade-in"
          style={{ animationDelay: "0.4s" }}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-100 dark:bg-orange-900">
                <TrendingUp className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Study Streak
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {stats.studyStreak} days
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
        {/* Recent Decks */}
        <div className="lg:col-span-2">
          <div className="card">
            <div className="border-b border-gray-200 px-6 py-4 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Recent Decks
              </h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {recentDecks.map((deck) => (
                  <div
                    key={deck.id}
                    className="flex items-center justify-between rounded-lg border border-gray-200 p-4 dark:border-gray-700"
                  >
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {deck.title}
                      </h4>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {deck.cardCount} cards • Last studied {deck.lastStudied}
                      </p>
                      <div className="mt-2">
                        <Progress
                          value={deck.progress * 100}
                          size="sm"
                          animated
                        />
                      </div>
                    </div>
                    <div className="ml-4">
                      <Link to={`/study/${deck.id}`}>
                        <Button size="sm">Study</Button>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-6">
                <Link to="/decks">
                  <Button variant="outline" className="w-full">
                    View All Decks
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Due Cards & Quick Actions */}
        <div className="space-y-6">
          {/* Due Cards */}
          <div className="card">
            <div className="border-b border-gray-200 px-6 py-4 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Cards Due for Review
              </h3>
            </div>
            <div className="p-6">
              <div className="space-y-3">
                {dueCards.map((item) => (
                  <div
                    key={item.id}
                    className="flex items-center justify-between"
                  >
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {item.deckTitle}
                    </span>
                    <Badge variant="error" size="sm">
                      {item.count}
                    </Badge>
                  </div>
                ))}
              </div>

              <div className="mt-6">
                <Link to="/study">
                  <Button
                    className="w-full"
                    leftIcon={<Brain className="h-4 w-4" />}
                  >
                    Start Studying
                  </Button>
                </Link>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="card">
            <div className="border-b border-gray-200 px-6 py-4 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Quick Actions
              </h3>
            </div>
            <div className="p-6 space-y-3">
              <Link to="/decks/new">
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  leftIcon={<Plus className="h-4 w-4" />}
                >
                  Create New Deck
                </Button>
              </Link>

              <Link to="/decks/public">
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  leftIcon={<BookOpen className="h-4 w-4" />}
                >
                  Browse Public Decks
                </Button>
              </Link>

              <Link to="/analytics">
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  leftIcon={<TrendingUp className="h-4 w-4" />}
                >
                  View Analytics
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
